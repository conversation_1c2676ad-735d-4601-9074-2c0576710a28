#!/bin/bash

# Run XBIT Agent with Environment Configuration
# Usage: ./scripts/run.sh [environment] [command]
# Example: ./scripts/run.sh local
# Example: ./scripts/run.sh docker build

set -e

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Default values
ENV=${1:-local}
COMMAND=${2:-run}

# Change to project directory
cd "$PROJECT_DIR"

# Load environment variables
source "$SCRIPT_DIR/load-env.sh" "$ENV"

# Function to run the application
run_app() {
    echo "Starting XBIT Agent..."
    
    # Check if binary exists
    if [ ! -f "bin/xbit-agent" ]; then
        echo "Binary not found. Building..."
        make build
    fi
    
    # Run the application
    ./bin/xbit-agent
}

# Function to build the application
build_app() {
    echo "Building XBIT Agent..."
    make build
}

# Function to run migrations
run_migrations() {
    echo "Running database migrations..."
    
    # Check if atlas is installed
    if ! command -v atlas &> /dev/null; then
        echo "Atlas CLI not found. Please install it first:"
        echo "  macOS: brew install ariga/tap/atlas"
        echo "  Linux: curl -sSf https://atlasgo.sh | sh"
        exit 1
    fi
    
    # Apply migrations
    atlas migrate apply --url "$DATABASE_URL" --dir file://migrations
}

# Function to generate migrations
generate_migration() {
    echo "Generating database migration..."
    
    # Check if atlas is installed
    if ! command -v atlas &> /dev/null; then
        echo "Atlas CLI not found. Please install it first:"
        echo "  macOS: brew install ariga/tap/atlas"
        echo "  Linux: curl -sSf https://atlasgo.sh | sh"
        exit 1
    fi
    
    # Generate migration
    atlas migrate diff --env gorm
}

# Function to run tests
run_tests() {
    echo "Running tests..."
    go test ./...
}

# Function to clean build artifacts
clean() {
    echo "Cleaning build artifacts..."
    rm -rf bin/
    rm -rf log/
}

# Main command handler
case "$COMMAND" in
    "run")
        run_app
        ;;
    "build")
        build_app
        ;;
    "migrate")
        run_migrations
        ;;
    "migrate:generate")
        generate_migration
        ;;
    "test")
        run_tests
        ;;
    "clean")
        clean
        ;;
    "dev")
        # Development mode with auto-reload (if air is installed)
        if command -v air &> /dev/null; then
            echo "Starting development server with auto-reload..."
            air -c .air.toml
        else
            echo "Air not found. Running normally..."
            run_app
        fi
        ;;
    *)
        echo "Usage: $0 [environment] [command]"
        echo ""
        echo "Environments:"
        echo "  local      - Local development (default)"
        echo "  docker     - Docker environment"
        echo "  production - Production environment"
        echo ""
        echo "Commands:"
        echo "  run              - Run the application (default)"
        echo "  build            - Build the application"
        echo "  migrate          - Run database migrations"
        echo "  migrate:generate - Generate new migration"
        echo "  test             - Run tests"
        echo "  clean            - Clean build artifacts"
        echo "  dev              - Development mode with auto-reload"
        echo ""
        echo "Examples:"
        echo "  $0 local run"
        echo "  $0 docker build"
        echo "  $0 production migrate"
        exit 1
        ;;
esac

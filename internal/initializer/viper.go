package initializer

import (
	"fmt"
	"os"

	"github.com/spf13/viper"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
)

func Viper(path ...string) *viper.Viper {
	var config string

	if len(path) == 0 {
		flag := "config.yaml"
		config = flag
		if configEnv := os.Getenv("GVA_CONFIG"); configEnv != "" {
			config = configEnv
			fmt.Printf("Using GVA_CONFIG environment variable, config path: %v\n", config)
		} else {
			fmt.Printf("Using default config value, config path: %v\n", config)
		}
	} else {
		config = path[0]
		fmt.Printf("Using command line value, config path: %v\n", config)
	}

	v := viper.New()
	v.SetConfigFile(config)
	v.SetConfigType("yaml")
	err := v.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("Fatal error config file: %s \n", err))
	}
	v.WatchConfig()

	if err = v.Unmarshal(&global.GVA_CONFIG); err != nil {
		panic(err)
	}

	// Root path adaptability
	// Find the corresponding migration location based on root position to ensure root path is valid
	global.GVA_CONFIG.Autocode.Root, _ = os.Getwd()

	// Only log if logger is initialized (avoid nil pointer dereference)
	if global.GVA_LOG != nil {
		global.GVA_LOG.Info("config file loaded successfully")
	} else {
		fmt.Println("config file loaded successfully")
	}
	return v
}

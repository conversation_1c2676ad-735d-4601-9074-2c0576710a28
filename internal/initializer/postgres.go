package initializer

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// GormPgSql Initialize Postgresql database
func GormPgSql() *gorm.DB {
	p := global.GVA_CONFIG.Pgsql
	return initPgSqlDatabase(p)
}

// GormPgSqlByConfig Initialize Postgresql database by specifying parameters
func GormPgSqlByConfig(p config.Pgsql) *gorm.DB {
	return initPgSqlDatabase(p)
}

func initPgSqlDatabase(p config.Pgsql) *gorm.DB {
	if p.Dbname == "" {
		return nil
	}
	pgsqlConfig := postgres.Config{
		DSN:                  p.Dsn(),
		PreferSimpleProtocol: false,
	}
	if db, err := gorm.Open(postgres.New(pgsqlConfig), gormConfig(p.Prefix, p.Singular)); err != nil {
		return nil
	} else {
		db.InstanceSet("gorm:table_options", "ENGINE="+p.Engine)
		sqlDB, _ := db.DB()
		sqlDB.SetMaxIdleConns(p.MaxIdleConns)
		sqlDB.SetMaxOpenConns(p.MaxOpenConns)
		return db
	}
}

# Steps to develop using graphql
The dev flow with `gql` `gorm` `atlas`:
```
Define GraphQL Schema (schema.graphqls)
          |
          v
Run gqlgen Code Generation (gqlgen generate)
          |
          v
Generated Files (generated.go, models_gen.go, schema.resolvers.go)
          |
          v
Implement Resolvers (custom_resolver.go)
          |
          v
Initialize Resolvers in Main Resolver (resolver.go)
          |
          v
Wire Resolvers to Generated Code (schema.resolvers.go)
          |
          v
Regenerate gqlgen Code (if schema changes)
          |
          v
Run and Test the Server (go run main.go)
          |
          v
Deploy the Application
```

```mermaid
---
title: flow and relation
---
flowchart TD
  app/resolvers -- has --> todo_r(app/resolvers/todo.*.resolver.go)
  app/resolvers --> user_r(app/resolvers/user.*.resolver.go)
  
  todo_r -- be included --> schema.resolvers.go
  todo_r -- be included --> resolver.go
  user_r -- use in --> schema.resolvers.go
  resolver.go -- as input --> gen(cli: make gen-gql = gqlgen generate)
  schema.resolvers.go -- as input --> gen
  schema.graphqls -- as input --> gen
  gen --> gen_output

  gen_output 
    -- dependency injection --> resolver(resolver.go) 
    -- be included --> internal/server/graphql-server.go

  %% GORM
  user_table(internal/models/user_model/user.go) 
    -- be included --> user_repository
    -- be included --> user_service
    -- be included --> user_r
  user_table -- atlas --> DB[(user table)]

  user_table -- convert by ChatGPT --> schema.graphqls("schemas/*.gql --> schema.graphqls")

```

## 1. Create your graphql schema
Golang eco is somehow lack of generation tool, so plz do it by your self

The root schema here: [schema.graphqls](schema.graphqls)

Each module, you need to create a separate schema,
create new schema file here `internal/graph/schemas`

### How to create .gql file
use ChatGPT to generate .gql file from gorm model
sth like this:
```graphql
type Config {
    id: ID!
    key: String!
    value: JSON!
    createdAt: String
    updatedAt: String
    deletedAt: String
}

# Query root
type Query {
    # Get all configs
    configs: [Config!]!

    # Get a config by ID
    config(id: ID!): Config
}

# Mutation root
type Mutation {
    createConfig(key: String!, value: JSON!): Config!
    updateConfig(id: ID!, key: String, value: JSON): Config!=
    deleteConfig(id: ID!): Boolean!
}
```
Note that the `Query` and `Mutation` is root, 
so you need to manually move Query & Mutation into root file at
[internal/graph/schema.graphqls](internal/graph/schema.graphqls)


## 2. Generate graphql
```
STAGE=local make gen-gql
```
Input:
- [schemas/*.gql](schemas)
- [resolver.go](resolver.go)

Output:
- [models_gen.go](model%2Fmodels_gen.go)
- [generated.go](generated.go)  
- [schema.resolvers.go](schema.resolvers.go)
  - the existing code modified by you will not be overwritten if you dont remove / comment the schema
  - only the new placeholder code will be moved to the end of file. From this section onward:
    ```
    // !!! WARNING !!!
    // The code below was going to be deleted when updating resolvers
    ```

## 3. Define Resolver
Your resolver is just like your controller, at the `internal/resolvers` folder.

After gql-gen cmd, Look at `git status` you will manage to see new Query & Mutation generated in [schema.resolvers.go](schema.resolvers.go) 

Please implement them by referring to `internal/resolvers`


Then re generate graphql at step `2. Generate graphql`


## 4. Deploy
Please add those action in your deployment pipeline:
```

```

## 5. Check playground
- Playground: http://localhost:9000/api/graphql-dex/playground
- Endpoint: http://localhost:9000/api/graphql-dex

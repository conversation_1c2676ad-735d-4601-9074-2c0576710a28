Server = .
ServerName = xbit_agent

# Define variables
GOOS = linux   # Target OS (can be overridden)
GOARCH = amd64 # Target architecture
BinDir = ./bin

build:
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(GOOS) GOARCH=$(GOARCH) go build -o $(BinDir)/$(ServerName) cmd/graphql/main.go
	@echo "Server built for $(GOOS)/$(GOARCH) in $(BinDir)"

db-diff:
	# atlas migrate diff [flags] [name]
	atlas migrate diff --env gorm

db-rehash:
	atlas migrate hash

db-apply:
	./scripts/run.sh local migrate

db-apply-docker:
	./scripts/run.sh docker migrate

gqlgen:
	go run github.com/99designs/gqlgen generate --config gqlgen.yml

install-deps:
	go mod tidy

run:
	go run cmd/graphql/main.go

run-local:
	./scripts/run.sh local run

run-docker:
	docker-compose up -d

dev:
	./scripts/run.sh local dev

dev-air:
	air -c .air.toml

test:
	go test ./...

clean:
	rm -rf $(BinDir)

.PHONY: build db-diff db-rehash db-apply gqlgen install-deps run dev test clean
